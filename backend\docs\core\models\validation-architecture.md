### Model-Level Validation Architecture Specification

**1. Purpose & Role**
Model-level validation ensures data integrity and consistency directly at the database model level, prior to persistence. It acts as a final safeguard to prevent invalid or inconsistent data from being written to the database, independent of whether the data originated from the API, internal processes, or other sources.

**2. Location & Organization**

*   **Primary Location:** Validation logic is primarily implemented directly within the [SQLAlchemy ORM model classes](../models-architecture.md) themselves, located in `src/core/models/`. This keeps validation logic coupled with the data structures it applies to.
*   **Helper Modules (for complexity/reusability):** For complex validation routines, or validation logic shared across multiple models, helper functions or classes can be defined in a dedicated module, e.g., `src/core/models/validation_helpers.py`. These helpers would then be called by the validation methods within the ORM models.

**3. Mechanisms (SQLAlchemy-specific)**

Model-level validation leverages SQLAlchemy's capabilities to perform checks just before data is committed:

*   **SQLAlchemy ORM Events:**
    *   **`@event.listens_for(SomeModel, 'before_insert')`:** Used to run validation logic right before a new instance of `SomeModel` is inserted into the database.
    *   **`@event.listens_for(SomeModel, 'before_update')`:** Used to run validation logic right before an existing instance of `SomeModel` is updated in the database.
    *   **`@event.listens_for(SomeModel, 'before_flush')`:** Can be used for validations that might span multiple related objects being flushed together, or for complex checks that need to see the entire pending transaction.
    *   **Implementation:** These listeners often call private or static validation methods within the model class.

    **Example (SQLAlchemy Event Listener):**

    ```python
    # Example within a model class (e.g., project.py)
    from sqlalchemy.orm import Session
    from sqlalchemy import event
    from src.core.errors.exceptions import DataValidationError # Assuming custom exception

    # Define a simple validator function
    def validate_project_dates(mapper, connection, target):
        if target.start_date and target.end_date and target.start_date > target.end_date:
            raise DataValidationError(details=[{'loc': ['start_date', 'end_date'], 'msg': 'Start date cannot be after end date', 'type': 'value_error'}]
)

    # Register the listener for 'before_insert' and 'before_update' events
    @event.listens_for(Project, 'before_insert')
    def before_insert_listener(mapper, connection, target):
        validate_project_dates(mapper, connection, target)

    @event.listens_for(Project, 'before_update')
    def before_update_listener(mapper, connection, target):
        validate_project_dates(mapper, connection, target)
    ```

*   **Hybrid Properties / Descriptors:**
    *   Custom Python descriptors or SQLAlchemy Hybrid Properties can be used to encapsulate validation logic that runs whenever a specific attribute is set on a model instance. This is suitable for immediate feedback on attribute assignments.
*   **Custom Validator Methods:**
    *   Models can define public or private methods (e.g., `_validate_material_compatibility()`, `_check_unique_project_code()`) that encapsulate specific validation rules. These methods are then invoked by the event listeners or by other model methods.

**4. Types of Validation Performed**

*   **Internal Consistency:** Checks that rely on the values of multiple fields within the *same model instance* (e.g., `start_date` must be before `end_date` for a project).
*   **Format & Constraints (Beyond Pydantic):** Specific string patterns, range checks (e.g., `temperature` must be between -50 and 200 degrees Celsius), or custom enumeration checks that might not be fully expressed by database column types alone.
*   **Simple Business Rules:** Very basic business rules that are intrinsically tied to the model's data structure and integrity (e.g., a `Pipe` must have a positive `length`).
*   **Referential Integrity (Soft Checks):** While the database enforces foreign key constraints, model validation can perform softer checks or provide more user-friendly error messages *before* a hard database error occurs (e.g., ensuring a related `Component` exists before linking to it).

**5. Interaction with Other Layers**

*   **Repository Layer ([`src/core/repositories/`](../../repositories/repositories-architecture.md)):** Repositories are the immediate layer interacting with model instances. They will receive validated model instances, or encounter exceptions raised by model-level validation *before* attempting to commit changes to the database. [Repositories](../../repositories/repositories-architecture.md) themselves **do not** implement validation logic; they simply persist valid models.
*   **Service Layer ([`src/core/services/`](../../services/services-architecture.md)):** Services instantiate and manipulate ORM models. They are generally aware that model-level validation might occur and should be prepared to catch and potentially translate exceptions raised by model validators into higher-level application errors. [Services](../../services/services-architecture.md) are responsible for higher-level **business logic validation** that spans multiple models or involves complex workflows.
*   **API Layer ([`src/api/`](../../../api/api-architecture.md)):** The [API layer](../../../api/api-architecture.md) primarily deals with [Pydantic DTOs](../../schemas/schemas-architecture.md) for input validation. It is generally not directly involved in model-level validation, as this occurs deeper within the application's core before persistence.
*   **Pydantic Validation ([Schema Layer](../../schemas/schemas-architecture.md)):** Pydantic's `ValidationError` occurs when input data doesn't conform to a defined schema (often at the [API Layer](../../../api/api-architecture.md)'s request parsing).
    *   **Catch:** Pydantic's `ValidationError` occurs when input data doesn't conform to a defined schema (often at the API Layer's request parsing).
    *   **Action:** Catch `ValidationError` and convert it into your custom `DataValidationError` exception, populating its `metadata` (and potentially the `errors` list) with the detailed validation errors from Pydantic's `exc.errors()`.

**6. Key Principles**

*   **Data Integrity at Source:** Validation occurs as close as possible to the data persistence layer, acting as a final gatekeeper.
*   **Layered Validation:** Model-level validation complements [API-level (Pydantic DTO) validation](../../../api/api-architecture.md#4-core-responsibilities--functionality) and [Service Layer (business logic) validation](../../services/services-architecture.md). Each layer handles its specific scope of validation.
*   **Fail Fast:** Basic data integrity issues are caught early in the data pipeline, preventing invalid data from corrupting the database.
*   **Clear Error Messages:** Validation failures should raise specific exceptions (e.g., custom `ValidationError` from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy)) with informative messages, which can then be translated by the [Service Layer](../../services/services-architecture.md) or global [error handler](../../errors/errors-architecture.md#3-error-handling-middleware-srccoreerrorserror_handlingpy).
*   **Decoupling:** Model validation should ideally only depend on the model's own attributes or very specific related objects that are part of the model's consistency rules, avoiding reliance on external services or complex business context.
