# backend/core/database/session.py
"""
Database Session Management

This module handles SQLAlchemy session creation, management, and provides
dependency injection functions for FastAPI.
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional

from sqlalchemy import Engine
from sqlalchemy.orm import Session, sessionmaker, scoped_session
from sqlalchemy.exc import SQLAlchemyError

from .engine import get_engine

logger = logging.getLogger(__name__)

# Global session factory
_session_factory: Optional[sessionmaker] = None
_scoped_session: Optional[scoped_session] = None


def get_session_factory(engine: Optional[Engine] = None) -> sessionmaker:
    """
    Get or create a session factory.
    
    Args:
        engine: Optional engine instance. If None, uses the global engine.
        
    Returns:
        SQLAlchemy sessionmaker instance
    """
    global _session_factory
    
    if _session_factory is None or engine is not None:
        if engine is None:
            engine = get_engine()
        
        _session_factory = sessionmaker(
            bind=engine,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
        )
        logger.debug("Session factory created")
    
    return _session_factory


def get_scoped_session(engine: Optional[Engine] = None) -> scoped_session:
    """
    Get or create a scoped session for thread-safe operations.
    
    Args:
        engine: Optional engine instance. If None, uses the global engine.
        
    Returns:
        SQLAlchemy scoped_session instance
    """
    global _scoped_session
    
    if _scoped_session is None or engine is not None:
        session_factory = get_session_factory(engine)
        _scoped_session = scoped_session(session_factory)
        logger.debug("Scoped session created")
    
    return _scoped_session


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    Context manager for database sessions with automatic cleanup.
    
    Yields:
        SQLAlchemy Session instance
        
    Example:
        with get_db_session() as session:
            user = session.query(User).first()
    """
    session_factory = get_session_factory()
    session = session_factory()
    
    try:
        logger.debug("Database session created")
        yield session
        session.commit()
        logger.debug("Database session committed")
    except SQLAlchemyError as e:
        logger.error(f"Database error occurred, rolling back: {e}")
        session.rollback()
        raise
    except Exception as e:
        logger.error(f"Unexpected error occurred, rolling back: {e}")
        session.rollback()
        raise
    finally:
        session.close()
        logger.debug("Database session closed")


def get_db() -> Generator[Session, None, None]:
    """
    FastAPI dependency function for database sessions.
    
    This function is designed to be used with FastAPI's Depends() system.
    
    Yields:
        SQLAlchemy Session instance
        
    Example:
        @app.get("/users/")
        def get_users(db: Session = Depends(get_db)):
            return db.query(User).all()
    """
    with get_db_session() as session:
        yield session


def create_tables(engine: Optional[Engine] = None) -> None:
    """
    Create all database tables defined in the models.
    
    Args:
        engine: Optional engine instance. If None, uses the global engine.
        
    Note:
        This function should only be used for development or initial setup.
        In production, use Alembic migrations instead.
    """
    if engine is None:
        engine = get_engine()
    
    # Import Base here to avoid circular imports
    from backend.core.models.base import Base
    
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except SQLAlchemyError as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


def drop_tables(engine: Optional[Engine] = None) -> None:
    """
    Drop all database tables.
    
    Args:
        engine: Optional engine instance. If None, uses the global engine.
        
    Warning:
        This function will delete all data! Use with extreme caution.
    """
    if engine is None:
        engine = get_engine()
    
    # Import Base here to avoid circular imports
    from backend.core.models.base import Base
    
    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("All database tables dropped")
    except SQLAlchemyError as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


def reset_session_factory() -> None:
    """Reset the global session factory. Useful for testing."""
    global _session_factory, _scoped_session
    
    if _scoped_session is not None:
        _scoped_session.remove()
        _scoped_session = None
    
    _session_factory = None
    logger.debug("Session factory reset")
