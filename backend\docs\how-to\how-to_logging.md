# How-To: Implement Logging

This document explains how to effectively implement logging within the backend codebase to aid in debugging, monitoring, and auditing.

While centralized error handling takes care of *exceptions*, logging serves a broader purpose beyond just errors. It's crucial for:

* **Debugging:** Understanding code flow, variable states, and intermittent issues.
* **Monitoring:** Tracking application health, performance bottlenecks, and user activity.
* **Auditing (non-error related):** Recording significant events that aren't errors (e.g., "User X opened Project Y," "Data imported from file Z," "Calculation for Circuit A completed").
* **Informational Messages:** Providing feedback to users or developers about long-running processes or successful operations.

The best practice is to **integrate logging directly into each module where significant events or potential issues can occur.** This is done by obtaining a logger instance at the module level.

Here's how you should approach logging in modules *outside* the centralized error handling:

### 1. Centralized Logger Configuration (Already in `src/core/logging_config.py`)

Keep your `setup_logging` function and the `app_logger` instance in `src/core/logging_config.py`. This is your single source of truth for how logging is configured (e.g., file paths, formatters, global levels).

### 2. Obtaining a Logger Instance in Each Module

In every Python module (`.py` file) where you want to log something, you should obtain a logger instance at the top of the file. The standard practice is to use `logging.getLogger(__name__)`.

```python
# src/core/services/project_service.py (Example)
import logging
from src.core.repositories.project_repository import ProjectRepository
from src.core.errors.exceptions import ProjectNotFoundError, DatabaseError
from src.core.schemas.project_schemas import ProjectCreateSchema
# Ensure your logging config is imported or app_logger is globally accessible
# For modularity, it's often better to import the logger object directly if pre-configured
from src.core.logging_config import app_logger as logger

# Using __name__ ensures logs from this module are tagged with its full path (e.g., 'src.core.services.project_service')
# This is highly recommended for filtering logs.
# If app_logger is configured to handle all sub-loggers, you can use it directly:
# logger = logging.getLogger(__name__)
# Or, if app_logger is the *root* logger you want to use for everything:
# from src.core.logging_config import app_logger as logger # This is what I used above for simplicity

class ProjectService:
    def __init__(self, project_repo: ProjectRepository):
        self.project_repo = project_repo
        logger.debug(f"ProjectService initialized with repository: {type(project_repo).__name__}") # Example debug log

    def create_project(self, project_data: ProjectCreateSchema):
        try:
            logger.info(f"Attempting to create new project: {project_data.name}")
            # ... business logic ...
            new_project_orm = self.project_repo.add(project_data.dict())
            logger.info(f"Project '{new_project_orm.name}' (ID: {new_project_orm.id}) created successfully by user {new_project_orm.created_by_user_id}.")
            return new_project_orm
        except Exception as e:
            logger.error(f"Failed to create project '{project_data.name}'. Error: {e}", exc_info=True) # Log exception details
            # Re-raise or translate exception for middleware/higher layers
            raise # The middleware will then catch and handle this
```

### Types of Logs and When to Use Them:

* **`logger.debug("Detailed internal state for debugging.")`**
    * **When:** For fine-grained diagnostic messages, variable values, or conditional logic branches. This is typically disabled in production.
    * **Where:** Repositories (SQL queries, data transformation details), Services (complex logic branches, intermediate calculation results), UI (detailed event handling).

* **`logger.info("Project 'MyProj' opened successfully.")`**
    * **When:** For general confirmation of expected events, successful operations, or significant application milestones.
    * **Where:** Service layer (successful creation/update/deletion), UI (user actions like opening/saving files), Import/Export modules (start/completion of import/export).

* **`logger.warning("Optional component 'Valve X' not found, proceeding without it.")`**
    * **When:** For unexpected but recoverable situations, potential problems that don't prevent execution, or deprecated features.
    * **Where:** Services (handling missing optional data, non-critical validation issues), Repositories (minor data inconsistencies).

* **`logger.error("Failed to connect to database: [Connection Error Details].")`**
    * **When:** For significant errors that prevent a particular operation from completing, but don't crash the entire application (e.g., network issues, invalid input from external source, failed calculation).
    * **Where:** Any layer where an exception is caught and indicates a problem that *should* be fixed or investigated. **Crucially, when you catch an exception and log it with `logger.error()`, consider if you should re-raise it or translate it into a custom `BaseApplicationException` for the centralized error handler to catch and process further.**

* **`logger.critical("Application failed to start. Exiting.")`**
    * **When:** For very severe errors that indicate a fundamental failure of the application or system (e.g., out of memory, critical file corruption). Usually leads to application termination.
    * **Where:** Application entry point (`main.py`) for startup failures, core system components.

* **`logger.exception("An unhandled exception occurred in ...", exc_info=True)`**
    * **When:** This is a special `ERROR` level call that automatically includes the current exception information and stack trace in the log output.
    * **Where:** Primarily used within your **centralized error handling middleware** (`src/middleware/error_handling.py`) for catching unexpected exceptions, but can also be used in `try...except` blocks in other modules when you want to explicitly log a traceback *before* re-raising or handling the error.

### Integration with the Centralized Error Handler:

The key is that **logging an `ERROR` or `CRITICAL` in a module typically indicates that an exception occurred.** Your centralized error handler then acts as the **final catcher and formatter** for these exceptions, ensuring they are not only logged but also transformed into consistent error responses for the UI.

So, the flow would be:
1.  **Module A** (e.g., `project_service.py`): Performs some logic.
2.  **Module A** encounters an issue:
    * If it's an **expected error** (e.g., "Project not found"), it `raises` a custom exception (`ProjectNotFoundError`). This custom exception contains all the necessary details for logging.
    * If it's an **unexpected low-level error** (e.g., `sqlalchemy.exc.IntegrityError`), Module A (or its repository) `catches` it, `logs` it with `logger.error(..., exc_info=True)` for full traceback, and then `raises` a custom `DatabaseError` (or similar) to propagate a standardized error type.
3.  **Error Handling Middleware**: Catches the raised `BaseApplicationException` (or general `Exception` for truly unhandled ones).
4.  **Middleware's Logging Role**: Extracts information from the exception (which already has contextual data if it's a custom exception), adds more context from the "request" (e.g., user, action), logs the error using `app_logger`, and then transforms it into a `ErrorResponseSchema` for the UI.

This separation ensures that modules focus on their core logic (and raising appropriate exceptions), while the error handling middleware focuses on universal logging, translation, and presentation of errors.