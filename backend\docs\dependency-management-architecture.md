### Dependency Management Architecture Specification

**1. Purpose & Role**
Dependency Management, powered by FastAPI's dependency injection (DI) system, is a core architectural principle that promotes **modularity, testability, reusability, and maintainability**. Its primary purpose is to:
* **Invert Control (IoC):** Instead of components creating their own dependencies, dependencies are "injected" or provided to them.
* **Loose Coupling:** Decouple components from their concrete implementations, allowing them to depend on abstract interfaces or specific types.
*   **Resource Management:** Manage the lifecycle of resources (e.g., [database sessions per request](../core/database/architecture.md), singleton [service instances](../core/services/services-architecture.md)).
*   **Simplify Code:** Keep business logic cleaner by offloading dependency resolution to the framework.
*   **Facilitate Testing:** Enable easy mocking of dependencies during unit and integration testing.

**2. Framework Choice (FastAPI's `Depends`)**
* **FastAPI's `Depends`:** The native dependency injection system built into FastAPI (powered by Starlette).
* **Justification:** Seamless integration with FastAPI's route handlers, automatic type hinting-based resolution, robust and performant.

**3. Core Responsibilities & Functionality**

*   **Dependency Providers:** Functions (or callable classes) that define how a specific dependency is created, initialized, or retrieved. These providers are typically `async` functions for asynchronous resources.
*   **Automatic Resolution:** [FastAPI](.../api/api-architecture.md) automatically resolves and injects dependencies into function parameters based on type hints and `Depends()`.
*   **Lifecycle Management:**
*   **Per-Request Scope:** [Database sessions](../core/database/architecture.md) (`get_db`) are typically created per request and automatically closed after the request is processed, ensuring isolation.
*   **Singleton Scope:** [Service instances](../core/services/services-architecture.md) (`get_project_service`) are typically created once per application lifecycle (or per specific context) and reused, often injecting other singleton dependencies ([like repositories](../core/repositories/repositories-architecture.md)).
*   **Resource Provisioning:** Provides instances of:
    * Database sessions (`Session`).
    * Service Layer instances (e.g., `ProjectService`).
    * Repository Layer instances (e.g., `ProjectRepository`).
    * External clients (e.g., `RedisClient`).
    * Configuration objects (though `settings` is often a direct import, DI can also provide it).
    * Contextual data (e.g., `current_user` from authentication).
* **Error Handling:** Dependency providers can raise exceptions if dependencies cannot be resolved or initialized, leading to appropriate HTTP error responses (e.g., `500 Internal Server Error`).

**4. Key Dependency Patterns & Providers**

*   [Database sessions](../core/database/architecture.md) (`Session`).
*   [Service Layer instances](../core/services/services-architecture.md) (e.g., `ProjectService`).
*   [Repository Layer instances](../core/repositories/repositories-architecture.md) (e.g., `ProjectRepository`).
*   External clients (e.g., `RedisClient`).
*   [Configuration objects](../config/config-architecture.md) (though `settings` is often a direct import, DI can also provide it).
*   Contextual data (e.g., `current_user` from [authentication](../security/security-architecture.md)).

*   **Database Session Provider (`src/core/database/dependencies.py`):**
    ```python
    # Example:
    from sqlalchemy.ext.asyncio import AsyncSession
    from src.core.database.session import get_async_session_maker

    async def get_db() -> AsyncSession:
        async_session_maker = get_async_session_maker() # Get configured session maker from app state
        async with async_session_maker() as session:
            yield session
    ```
*   **Repository Instance Providers (`src/core/repositories/dependencies.py`):**
    ```python
    # Example:
    from fastapi import Depends
    from sqlalchemy.ext.asyncio import AsyncSession
    from src.core.repositories.project_repository import ProjectRepository
    from .database_dependencies import get_db # Assuming get_db is defined in database_dependencies

    def get_project_repository(db: AsyncSession = Depends(get_db)) -> ProjectRepository:
        return ProjectRepository(session=db)
    ```
* **Service Instance Providers (`src/core/services/dependencies.py`):**
    ```python
    # Example:
    from fastapi import Depends
    from src.core.services.project_service import ProjectService
    from src.core.repositories.dependencies import get_project_repository

    def get_project_service(
        project_repo: ProjectRepository = Depends(get_project_repository)
    ) -> ProjectService:
        return ProjectService(project_repository=project_repo)
    ```
*   **Current User Provider (`src/middleware/security.py` / `src/api/v1/auth_routes.py`):**

    See the [Security Layer Architecture](../security/security-architecture.md) for more details on authentication.

    ```python
    # Example:
    from fastapi import Depends, HTTPException, status
    from src.core.models.user_model import User # Assuming this model exists
    from src.core.security.jwt_utils import verify_token # Placeholder - Assuming JWT utils exist
    # from src.core.repositories.user_repository import UserRepository # Assuming user repository exists
    # from src.core.dependencies import get_user_repository # Assuming user repository dependency

    # from fastapi.security import OAuth2PasswordBearer # Assuming OAuth2 scheme
    # oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token") # Assuming token URL

    async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
        # Logic to decode/verify token and fetch user from DB/cache
        user_id = verify_token(token)
        if user_id is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
        # Fetch user from repository (example)
        # user_repo: UserRepository = Depends(get_user_repository)
        # user = user_repo.get_user_by_id(user_id)
        # if user is None:
        #     raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
        # return user
        pass # Placeholder for actual logic
    ```

**5. Interaction with Other Layers**

*   **API Layer ([`src/api/`](api/api-architecture.md) - Primary Consumer):** All [API endpoint functions](api/api-architecture.md) (`@app.get`, `@router.post`) will use `Depends()` in their parameters to receive instances of [services](../core/services/services-architecture.md), [database sessions](../core/database/architecture.md), and authenticated users.
*   **Service Layer:** While [services](../core/services/services-architecture.md) *consume* dependencies (e.g., [repositories](../core/repositories/repositories-architecture.md)) via their constructors, these [service instances](../core/services/services-architecture.md) are themselves provided *through* dependency injection (e.g., `get_project_service`). Complex service methods might also define `Depends()` if they require unique per-call resources not passed through their constructor.

    See the [Service Layer Architecture](../core/services/services-architecture.md) for more details.
*   **Repository Layer:** [Repositories](../core/repositories/repositories-architecture.md) typically receive their primary dependency (the `Session` object) through dependency injection (from their `get_repository` provider).

    See the [Repository Layer Architecture](../core/repositories/repositories-architecture.md) for more details.

*   **Lifespan Events (`src/app.py`):** Resources initialized during application startup (e.g., SQLAlchemy `Engine`, Redis client) are then made available to dependency providers for injection.

    See the [Lifespan Events Architecture](lifespan-architecture.md) for more details.

*   **Testing Framework:** Dependency injection is fundamental for unit and integration testing. Test clients can easily override dependencies to provide mock objects or specific test data, isolating components for testing.

**6. Key Principles**

* **Inversion of Control:** Components declare what they need, and the framework provides it.
* **Loose Coupling:** Components depend on interfaces or types, not concrete implementations, making them easier to swap or upgrade.
* **Testability:** Enables easy mocking of dependencies, simplifying unit and integration tests.
* **Readability:** Function signatures clearly state the required dependencies, improving code comprehension.
* **Reusability:** Dependency providers can be reused across multiple parts of the application.