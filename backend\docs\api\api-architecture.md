### API Layer Architecture Specification: Heat Tracing Design Application (Revised with Versioning)

**1. Purpose & Role**
The API Layer serves as the **HTTP/RESTful interface** for the application's backend services. It acts as the primary communication gateway between the React frontend (running within Electron) and the system's core Python business logic. Its main responsibilities include routing client requests to appropriate handlers, validating data, orchestrating service calls, serializing responses, and translating errors into a standardized HTTP format.

**2. Framework Choice**
* **FastAPI**: Selected due to its strengths in building high-performance APIs, native Pydantic integration for data validation and serialization, automatic OpenAPI (Swagger UI / ReDoc) documentation generation, and robust dependency injection system. These features streamline API development and ensure clear data contracts.

**3. Structure (`src/api/`)**
The API layer is structured into versioned modules, promoting modularity and facilitating future API evolution. Each resource group has its dedicated router file. The overall FastAPI application instance, typically defined in `src/app.py` (or `main.py`), acts as the root orchestrator for the API layer.

```
src/api/
├── __init__.py                     # Initializes the API package
├── main_router.py                  # Aggregates and includes all versioned API routers
└── v1/                             # Directory for API Version 1
    ├── __init__.py                 # Initializes v1 API sub-package
    ├── project_routes.py           # Endpoints for Project management (CRUD)
    ├── component_routes.py         # Endpoints for Component catalog management (CRUD, search)
    ├── heat_tracing_routes.py      # Endpoints for Pipe, Vessel, HTCircuit, ControlCircuit management (CRUD)
    ├── electrical_routes.py        # Endpoints for ElectricalNode, CableRoute management (CRUD)
    ├── switchboard_routes.py       # Endpoints for Switchboard management (CRUD)
    ├── feeder_routes.py            # Endpoints for Feeder management (CRUD)
    ├── calculation_routes.py       # Endpoints for triggering & retrieving calculations (e.g., heat loss, sizing)
    ├── report_routes.py            # Endpoints for generating & retrieving reports (BOMs, schedules)
    ├── import_export_routes.py     # Endpoints for data import (upload) and export (download)
    ├── user_routes.py              # Endpoints for User management (CRUD, profile)
    ├── auth_routes.py              # Endpoints for authentication (login, logout, token refresh/validation)
    ├── audit_routes.py             # Endpoints for viewing application audit logs (admin-only)
    └── health_check_routes.py      # Endpoints for API health and status checks
```

**4. Core Responsibilities & Functionality**

* **Routing:** Maps incoming HTTP requests (HTTP method + URL path) to specific `async` Python handler functions defined within the route modules.
* **Request Validation:** Automatically validates incoming data using **Pydantic schemas** (from [`src/core/schemas/`](../core/schemas/schemas-architecture.md)). This includes:
    * **Request Bodies:** JSON payloads for `POST`, `PUT`, `PATCH` requests.
    * **Query Parameters:** URL query strings.
    * **Path Parameters:** Variables embedded in the URL path.
    * Any validation failures are automatically translated into `HTTP 400 Bad Request` or `HTTP 422 Unprocessable Entity` responses.

    **Example (Request Body Validation):**

    ```python
    # Example in src/api/v1/project_routes.py
    from fastapi import APIRouter, Depends
    from src.core.schemas.project_schemas import ProjectCreate # Assuming this schema exists
    from src.core.services.project_service import ProjectService # Assuming this service exists
    from src.core.dependencies import get_project_service # Assuming this dependency exists

    router = APIRouter()

    @router.post("/projects/")
    async def create_project(
        project: ProjectCreate, # Pydantic model for request body validation
        project_service: ProjectService = Depends(get_project_service)
    ):
        # project object is automatically validated by FastAPI
        new_project = project_service.create_project(project)
        return new_project # FastAPI will serialize the response using a response_model if defined
    ```

* **Dependency Injection (`fastapi.Depends`):** Provides a robust mechanism for injecting resources and functionality into API handler functions, ensuring handlers remain lean and testable. Injected dependencies include:

    For more details, see the [Dependency Management Architecture](../../dependency-management-architecture.md).
    * Database sessions (`db: Session = Depends(get_db)`).
    * Instances of Service Layer classes (`project_service: ProjectService = Depends(get_project_service)`).
    * Contextual data provided by middleware (e.g., `current_user: User = Depends(get_current_user)` from `SecurityMiddleware`).
* **Orchestration (Thin Layer):** API handlers serve as thin controllers. Their primary role is to:
    * Receive validated input from the request.
    * Delegate all business logic and complex operations to the appropriate method(s) in the **Service Layer**.
    * Receive results from the Service Layer.
* **Response Serialization:** Uses Pydantic schemas (e.g., `ProjectReadSchema`) to automatically serialize Python objects (typically ORM models from the Service Layer) into consistent JSON responses. This also implicitly validates the outgoing data structure.
* **Error Translation:** Catches application-specific exceptions (custom exceptions from [`src/core/errors/exceptions.py`](../core/errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy)) and re-raises them as FastAPI's `HTTPException` (e.g., `raise HTTPException(status_code=404, detail="Project not found")`). These `HTTPException` instances are then caught by the global error handling middleware, which formats them into standardized JSON error responses based on [`src/core/schemas/error.py`](../core/schemas/schemas-architecture.md#4-error-response-schemas-srccoreschemaserrorpy).

    For a detailed explanation of error handling, see the [Error and Exception Handling Architecture](../core/errors/errors-architecture.md).

* **Automatic Documentation:** FastAPI inspects the route definitions, Pydantic schemas, and type hints to automatically generate and host interactive OpenAPI documentation (Swagger UI at `/docs` and ReDoc at `/redoc`), providing a live and accurate API reference for frontend developers.

**5. API Versioning Strategy**

API versioning is crucial for managing the evolution of the backend API without introducing breaking changes to existing client applications.

* **Strategy: URL Path Versioning**
    * The chosen strategy is to embed the API version directly into the URL path (e.g., `/api/v1/projects`). This is a common, explicit, and easy-to-understand approach for both developers and consumers.
    * **Justification:**
        * **Clarity:** The API version is immediately obvious in the URL.
        * **Simplicity:** Easy to implement in FastAPI by using `APIRouter` prefixes.
        * **Discoverability:** Clients can easily see and access different API versions.
* **Implementation Details:**
    * All version-specific route definitions are encapsulated within dedicated directories (e.g., `src/api/v1/`).
    * Each version's `APIRouter` is independently defined (e.g., `v1_project_router`).
    * The `src/api/main_router.py` aggregates these versioned routers using FastAPI's `app.include_router()` method with the appropriate `prefix` (e.g., `api_router.include_router(v1_project_router, prefix="/v1/projects")`).
    * **Future Versions:** When API changes require breaking compatibility, a new version directory (e.g., `src/api/v2/`) will be created, allowing both `/v1/` and `/v2/` endpoints to coexist.
* **Client Responsibility:** Frontend clients must explicitly request the desired API version by including the version prefix in their request URLs.
* **Deprecation and Retirement:** Older API versions will be supported for a defined deprecation period (e.g., 6-12 months) after a new version is released, providing clients ample time to migrate. Communication of deprecation will be clearly documented.

**6. Interaction with Other Layers**

* **Application Root (`src/app.py` / `main.py`):** This file is responsible for creating the main FastAPI application instance, registering all global middleware, initializing the database connection, and including the `main_router.py` to expose the API endpoints.
* **Middleware Layer (`src/middleware/`):** The API Layer integrates directly with the [Middleware Layer](../../middleware/middleware-architecture.md). Middleware functions process requests *before* they reach API handlers (e.g., authentication, context injection) and *after* handlers return responses (e.g., logging, error handling, caching).
* **Service Layer (`src/core/services/`):** This is the primary collaborator for the [Service Layer](../core/services/services-architecture.md). API handlers call methods in the Service Layer to execute all business logic, calculations, and data manipulation. API handlers *do not* directly interact with repositories or the database session.
* **Schema Layer (`src/core/schemas/`):** Heavily utilized by the [Schema Layer](../core/schemas/schemas-architecture.md) for:
    * Defining the structure and validation rules for incoming request payloads.
    * Specifying the structure and content of outgoing JSON responses.
    * Providing standardized schemas for error messages.

---