# src/app.py
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse

# ... import other middleware as needed
# Import main API router
from backend.api.main_router import api_router

# Import your core modules
from backend.config.settings import settings
from backend.core.logging_config import app_logger as logger
from backend.core.models import init_db  # Import Base and init_db from your __init__.py
from backend.middleware.context import (
    ContextMiddleware,  # Assuming ContextMiddleware will handle request IDs too
)

# Import middleware
from backend.middleware.error_handling import handle_application_error
from backend.middleware.security import SecurityMiddleware


# --- Database Session Management ---
# This is a common pattern for FastAPI with SQLAlchemy
def get_db():
    """Dependency to get a SQLAlchemy session for a request."""
    # Session is configured in src/core/models/__init__.py
    from backend.core.models import Session as DBSession

    db = DBSession()
    try:
        yield db
    finally:
        db.close()


# --- Application Lifespan Events ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handles startup and shutdown events for the application.
    """
    logger.info("Application startup initiated.")
    try:
        # Initialize database
        engine, SessionClass = init_db(
            database_url=settings.DATABASE_URL, echo=settings.DB_ECHO
        )
        logger.info(f"Database initialized: {settings.DATABASE_URL}")

        # You might run database migrations here if not using a separate process
        # For a desktop app, Base.metadata.create_all(engine) might be sufficient on first run.
        # For prod web app, use Alembic.

        # Initialize any caches or other startup resources
        # cache_client = init_cache()
        # app.state.cache = cache_client # Store client for access in dependencies

        logger.info("Application startup complete.")
        yield  # Application runs
    except Exception as e:
        logger.critical(f"Application startup failed critically: {e}", exc_info=True)
        # Depending on environment, you might want to re-raise or exit here
        raise
    finally:
        logger.info("Application shutdown initiated.")
        # Clean up resources on shutdown
        # if app.state.cache:
        #     app.state.cache.close()
        logger.info("Application shutdown complete.")


# --- FastAPI Application Instance ---
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,  # Attach lifespan context manager
)

# --- Register Middleware ---
# Middleware runs in the order they are added (bottom-up execution on response)
# Order is crucial! Error handling typically goes near the top (bottom of chain)
# so it can catch exceptions from other middleware.
app.middleware("http")(ContextMiddleware)  # Inject context first
app.middleware("http")(SecurityMiddleware)  # Authentication depends on context
# app.middleware("http")(LoggingMiddleware) # Log before/after request processing
# app.middleware("http")(RateLimitingMiddleware) # Rate limit early
# app.middleware("http")(CachingMiddleware) # Cache response after processing


# Custom exception handler for application-wide errors
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler that uses the centralized error handling logic.
    """
    error_response_schema = handle_application_error(
        exc, context_info={"path": request.url.path, "method": request.method}
    )
    return JSONResponse(
        status_code=error_response_schema.status_code,
        content=error_response_schema.model_dump(),  # Use .dict() for Pydantic v1
    )


# --- Include API Routers ---
app.include_router(
    api_router, prefix="/api"
)  # All API routes will be prefixed with /api


# Example root endpoint
@app.get("/")
async def read_root():
    return {"message": f"Welcome to {settings.APP_NAME} API v{settings.APP_VERSION}"}
