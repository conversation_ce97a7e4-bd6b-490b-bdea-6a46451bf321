# How-To: Configure Application Settings

This document provides a quick guide on how to access and use application settings within the backend codebase.

Application settings are managed by the [Configuration Layer](../config/config-architecture.md) and loaded from various sources, including `.env` files and environment variables. For a detailed understanding of the configuration architecture, please refer to the [Configuration Layer Architecture documentation](../config/config-architecture.md).

To access settings in your code, import the `settings` object from `src.config.settings`:

```python
# Example in src/app.py or a service
from src.config.settings import settings

# Accessing a setting
if settings.DEBUG:
    print("Debug mode is enabled!")

# Accessing other settings
db_url = settings.DATABASE_URL
log_level = settings.LOG_LEVEL

# Example of using a setting in a function or class
def get_database_url():
    return settings.DATABASE_URL

class MyService:
    def __init__(self):
        self.api_key = settings.THIRD_PARTY_API_KEY # Assuming THIRD_PARTY_API_KEY is defined in settings

    def call_api(self):
        # Use self.api_key to make an API call
        pass
```

To define new settings, you need to add them to the `Settings` class in `src/config/settings.py`.