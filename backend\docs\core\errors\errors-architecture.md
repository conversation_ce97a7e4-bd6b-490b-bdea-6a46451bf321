## Error and Exception Handling Architecture: Detailed Implementation

Your proposed structure provides a highly organized and consistent way to manage errors throughout your application. This approach ensures that errors are not just arbitrary exceptions but well-defined, actionable events with meaningful metadata, benefiting both developers and end-users.

### 1. Error Factory ([`src/core/errors/error_factory.py`](#1-error-factory-srccoreerrorserror_factorypy-error-registry-srccoreerrorserror_registrypy--error-templates-srccoreerrorserror_templatespy)), Error Registry ([`src/core/errors/error_registry.py`](#1-error-factory-srccoreerrorserror_factorypy-error-registry-srccoreerrorserror_registrypy--error-templates-srccoreerrorserror_templatespy)), & Error Templates ([`src/core/errors/error_templates.py`](#1-error-factory-srccoreerrorserror_factorypy-error-registry-srccoreerrorserror_registrypy--error-templates-srccoreerrorserror_templatespy))

This trio forms the backbone of your centralized error definition system.

*   **Purpose:** To centralize the definition, creation, and consistency of application-specific errors. This ensures that errors are not just arbitrary exceptions but well-defined objects with meaningful metadata, facilitating uniform logging, reporting, and user feedback.
*   **Implementation:**

    *   **`src/core/errors/error_templates.py`**: Defines reusable message templates.

        ```python
        # src/core/errors/error_templates.py
        ERROR_MESSAGES = {
            "PROJECT_NOT_FOUND": "Project with ID '{project_id}' not found.",
            "COMPONENT_ALREADY_EXISTS": "Component '{name}' in category '{category}' already exists.",
            "VALIDATION_ERROR": "Input validation failed: {details}.",
            "DB_OPERATION_FAILED": "Database operation failed: {reason}.",
            "CALCULATION_INPUT_INVALID": "Invalid input for calculation: {details}.",
            "STANDARD_COMPLIANCE_FAILED": "Standard compliance check failed: {details}.",
            "REPORT_GENERATION_FAILED": "Report generation failed: {details}.",
            "DATA_IMPORT_FAILED": "Data import failed: {details}.",
            "UNAUTHORIZED_ACCESS": "Authentication required or invalid credentials.",
            "FORBIDDEN_ACTION": "You do not have permission to perform this action.",
            "GENERIC_SERVER_ERROR": "An unexpected internal error occurred. Please try again later.",
            # ... other templates as needed
        }
        ```

    *   **`src/core/errors/error_registry.py`**: Maps canonical error codes to their categories, message keys, and HTTP status codes.

        ```python
        # src/core/errors/error_registry.py
        from typing import Literal

        # Define error categories (e.g., for logging or UI display)
        ErrorCategory = Literal[
            "ClientError", "ServerError", "DatabaseError",
            "Validation", "Calculation", "Authentication", "Authorization",
            "Report", "Import"
        ]

        ERROR_REGISTRY = {
            "404_001": {"category": "ClientError", "message_key": "PROJECT_NOT_FOUND", "http_status": 404},
            "409_001": {"category": "ClientError", "message_key": "COMPONENT_ALREADY_EXISTS", "http_status": 409},
            "400_001": {"category": "Validation", "message_key": "VALIDATION_ERROR", "http_status": 400},
            "500_001": {"category": "DatabaseError", "message_key": "DB_OPERATION_FAILED", "http_status": 500},
            "422_001": {"category": "Calculation", "message_key": "CALCULATION_INPUT_INVALID", "http_status": 422},
            "422_002": {"category": "Calculation", "message_key": "STANDARD_COMPLIANCE_FAILED", "http_status": 422},
            "500_002": {"category": "Report", "message_key": "REPORT_GENERATION_FAILED", "http_status": 500},
            "400_002": {"category": "Import", "message_key": "DATA_IMPORT_FAILED", "http_status": 400},
            "401_001": {"category": "Authentication", "message_key": "UNAUTHORIZED_ACCESS", "http_status": 401},
            "403_001": {"category": "Authorization", "message_key": "FORBIDDEN_ACTION", "http_status": 403},
            "500_000": {"category": "ServerError", "message_key": "GENERIC_SERVER_ERROR", "http_status": 500},
            # ... more error codes as the application grows
        }
        ```

    *   **`src/core/errors/error_factory.py`**: Creates instances of your custom exceptions using the registry and templates.

        ```python
        # src/core/errors/error_factory.py
        from .exceptions import BaseApplicationException # Ensure BaseApplicationException is defined as below
        from .error_registry import ERROR_REGISTRY
        from .error_templates import ERROR_MESSAGES
        from typing import Any, Dict

        class ErrorFactory:
            @staticmethod
            def create_exception(code: str, **kwargs: Any) -> BaseApplicationException:
                error_info = ERROR_REGISTRY.get(code)
                if not error_info:
                    # Fallback for unregistered codes to prevent application crashes
                    return BaseApplicationException(
                        code="500_000",
                        detail=ERROR_MESSAGES["GENERIC_SERVER_ERROR"],
                        category="ServerError",
                        status_code=500,
                        metadata={"unregistered_code_attempted": code, **kwargs}
                    )

                message = ERROR_MESSAGES.get(error_info["message_key"], ERROR_MESSAGES["GENERIC_SERVER_ERROR"])
                formatted_message = message.format(**kwargs)

                return BaseApplicationException(
                    code=code,
                    detail=formatted_message,
                    category=error_info["category"],
                    status_code=error_info.get("http_status", 500),
                    metadata=kwargs # Store original kwargs for logging/debugging
                )

        ```

*   **Benefits:**
    *   **Consistency:** All errors have the same structure and messaging across the application.
    *   **Maintainability:** Easily add, modify, or deprecate error codes without touching application logic.
    *   **Localization (Future):** Message templates are easier to translate into different languages.
    *   **Documentation:** The registry serves as living documentation for all possible application errors.

---

### 2. Custom Exceptions ([`src/core/errors/exceptions.py`](#2-custom-exceptions-srccoreerrorsexceptionspy))

This layer defines a clear hierarchy of specific exception types, all inheriting from a common base, enabling fine-grained control and consistent error data.

*   **Purpose:** To define specific, semantically meaningful exception types that carry consistent error attributes (code, detail, category, status\_code, metadata).
*   **Implementation:**

    ```python
    # src/core/errors/exceptions.py
    from typing import Optional, Any, List, Dict
    from pydantic import BaseModel, Field

    # For Pydantic validation error details, aligning with FastAPI's structure
    class ValidationErrorDetail(BaseModel):
        loc: List[str | int] = Field(..., description="Location of the validation error in the input data.")
        msg: str = Field(..., description="Validation error message.")
        type: str = Field(..., description="Type of validation error.")

    class BaseApplicationException(Exception):
        """Base exception for all application-specific errors."""
        def __init__(self, code: str, detail: str, category: str = "ServerError", status_code: int = 500, metadata: Optional[Dict[str, Any]] = None):
            super().__init__(detail)
            self.code = code
            self.detail = detail
            self.category = category
            self.status_code = status_code
            self.metadata = metadata or {}

    # Domain-specific exceptions leveraging ErrorFactory
    class NotFoundError(BaseApplicationException):
        def __init__(self, resource_name: str = "Resource", identifier: Any = None):
            # Using error factory implicitly by passing known code
            factory_instance = ErrorFactory() # Or use a static method directly
            exc = factory_instance.create_exception(
                code="404_001", # Assuming this is the PROJECT_NOT_FOUND equivalent for generic NotFound
                resource_name=resource_name,
                identifier=identifier
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata=exc.metadata
            )

    class ProjectNotFoundError(NotFoundError):
        def __init__(self, project_id: str):
            super().__init__(
                resource_name="Project",
                identifier=project_id,
                # Explicitly override if registry has specific code for ProjectNotFound
                # Otherwise, it falls back to NotFoundError's factory call
            )
            # You might want to ensure a unique code for ProjectNotFound specifically if needed
            self.code = "404_001" # Direct assignment or refine factory call

    class DataValidationError(BaseApplicationException):
        """For semantically invalid input data, typically from Pydantic or custom schema checks."""
        def __init__(self, details: List[ValidationErrorDetail]):
            # Using error factory
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(
                code="400_001", # VALIDATION_ERROR
                details=details
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata={"validation_errors": [d.model_dump() for d in details]} if details else {}
            )

    class DatabaseError(BaseApplicationException):
        """Generic error for database operation failures."""
        def __init__(self, reason: str, original_exception: Optional[Exception] = None):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(
                code="500_001", # DB_OPERATION_FAILED
                reason=reason
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata={"reason": reason, "original_exception_type": type(original_exception).__name__ if original_exception else None}
            )

    class CalculationError(BaseApplicationException):
        """Raised when a calculation cannot be performed or yields invalid results."""
        def __init__(self, details: str, input_context: Optional[Dict[str, Any]] = None):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(
                code="422_001", # CALCULATION_INPUT_INVALID or a more generic CALCULATION_FAILED
                details=details
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata={"reason": details, "input_context": input_context}
            )

    class StandardComplianceError(CalculationError):
        """Raised when a design or calculation violates a specific engineering standard."""
        def __init__(self, details: str, violated_rules: Optional[List[str]] = None, input_context: Optional[Dict[str, Any]] = None):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(
                code="422_002", # STANDARD_COMPLIANCE_FAILED
                details=details
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata={"reason": details, "violated_rules": violated_rules, "input_context": input_context}
            )

    class ReportGenerationError(BaseApplicationException):
        """Raised when an error occurs during report generation."""
        def __init__(self, details: str):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(
                code="500_002", # REPORT_GENERATION_FAILED
                details=details
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata={"reason": details}
            )

    class ImportError(BaseApplicationException):
        """Raised when an error occurs during data import."""
        def __init__(self, details: str, error_list: Optional[List[Dict[str, Any]]] = None):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(
                code="400_002", # DATA_IMPORT_FAILED
                details=details
            )
            super().__init__(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata={"reason": details, "error_list": error_list}
            )

    # General Authentication/Authorization errors
    class UnauthorizedError(BaseApplicationException):
        def __init__(self):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(code="401_001") # UNAUTHORIZED_ACCESS
            super().__init__(**exc.__dict__) # Copy attributes from factory-created exception

    class ForbiddenError(BaseApplicationException):
        def __init__(self):
            factory_instance = ErrorFactory()
            exc = factory_instance.create_exception(code="403_001") # FORBIDDEN_ACTION
            super().__init__(**exc.__dict__)
    ```

* **Benefits:**
    * **Semantic Meaning:** Exceptions clearly convey what went wrong, not just that an error occurred.
    * **Catching Specific Errors:** Allows for more targeted `try-except` blocks.
    * **Unified Error Data:** All custom exceptions carry consistent attributes (code, detail, category, status\_code, metadata), simplifying handling downstream.

---

### 3. Error Handling Middleware (`src/middleware/error_handling.py`)

This component centralizes the processing of exceptions, transforming them into a consistent format for the user interface or any API consumers.

*   **Purpose:** To centralize the catching, logging, and transformation of exceptions into standardized error responses. For a desktop application, "middleware" can be thought of as a global exception hook, or decorators/context managers applied at the boundaries of major operations.
*   **Implementation:**

    ```python
    # src/middleware/error_handling.py
    import logging
    from src.core.errors.exceptions import BaseApplicationException, DataValidationError, ValidationErrorDetail
    from src.core.schemas.error import ErrorResponseSchema # Pydantic schema for output
    from src.config.logging_config import setup_logging # Assuming you have this configured
    from typing import Optional, Dict, Any, List

    logger = setup_logging() # Initialize logger

    def handle_application_error(
        exc: Exception,
        context_info: Optional[Dict[str, Any]] = None # e.g., {'action': 'save_project', 'user_id': 'xyz'}
    ) -> ErrorResponseSchema:
        """
        Catches and processes application exceptions.
        Transforms them into a standardized error response and logs them.
        """
        error_response: ErrorResponseSchema
        log_level: int = logging.ERROR # Default to ERROR for unexpected issues

        if isinstance(exc, BaseApplicationException):
            error_response = ErrorResponseSchema(
                code=exc.code,
                detail=exc.detail,
                category=exc.category,
                status_code=exc.status_code,
                metadata=exc.metadata
            )
            # Add specific validation details if it's a DataValidationError
            if isinstance(exc, DataValidationError):
                if exc.metadata and "validation_errors" in exc.metadata:
                    error_response.errors = [
                        ErrorDetail(loc=d['loc'], msg=d['msg'], type=d['type'])
                        for d in exc.metadata["validation_errors"]
                    ]

            # Adjust log level based on HTTP status code convention
            if 200 <= exc.status_code < 500: # Client errors are often INFO/WARNING in logs
                log_level = logging.INFO
            else: # Server errors are always ERROR/CRITICAL
                log_level = logging.ERROR

            logger.log(log_level, f"Application Exception ({exc.code}): {exc.detail}",
                       extra={"error_metadata": exc.metadata, "context_info": context_info})

        elif isinstance(exc, Exception): # Catch all other unexpected Python exceptions
            # Fallback for unhandled, unexpected errors
            error_response = ErrorResponseSchema(
                code="500_000", # Generic internal server error
                detail="An unexpected internal error occurred. Please try again later.",
                category="ServerError",
                status_code=500,
                metadata={"original_exception_type": type(exc).__name__, "message": str(exc)}
            )
            # Log full traceback for unexpected errors
            logger.exception(f"UNHANDLED EXCEPTION: {str(exc)}", extra={"context_info": context_info})

        else: # Should not happen, but for type completeness
            error_response = ErrorResponseSchema(
                code="500_000",
                detail="An unknown error occurred.",
                category="ServerError",
                status_code=500
            )

        return error_response

    # Example: A decorator for UI actions or service calls in a desktop app
    def exception_handler_decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # In a UI, 'context_info' might contain current screen, button ID, user input summary
                # For an API, this would be a FastAPI exception handler or middleware.
                # Here, we're simulating a desktop context.
                action_context = {"action_name": func.__name__, "args": args[:1]} # Example context
                error_resp = handle_application_error(e, context_info=action_context)
                # In a real desktop app, you'd trigger a UI display here (e.g., messagebox)
                print(f"ERROR Display to User: Code={error_resp.code}, Detail='{error_resp.detail}'")
                # Depending on design, you might re-raise a specific UIException for UI framework to catch
                raise # Re-raise to propagate for higher-level handling if needed (e.g., app shutdown)

        return wrapper
    ```

*   **Benefits:**
    *   **Centralized Control:** All errors are processed through a single pipeline, ensuring consistent handling.
    *   **Consistent Logging:** Guarantees all errors are logged with relevant context for debugging.
    *   **Standardized Responses:** Ensures that the UI always receives error information in a predictable format.
    *   **Robustness:** Catches even unexpected errors, preventing application crashes and exposing raw stack traces to the user.

---

### 4. Error Response Schemas (`src/core/schemas/error.py`)

These [Pydantic schemas](../../schemas/schemas-architecture.md) define the exact structure of error messages returned to the UI or any API consumers, ensuring they are standardized and machine-readable.

*   **Purpose:** To define the precise structure of error messages, making them predictable and easy for consuming layers (UI, API clients) to parse and display.
*   **Implementation:**

    ```python
    # src/core/schemas/error.py
    from pydantic import BaseModel, Field
    from typing import Optional, List, Dict, Any

    class ErrorDetail(BaseModel):
        loc: Optional[List[str | int]] = Field(None, description="Location of the error, e.g., ['body', 'field_name']")
        msg: str = Field(..., description="A human-readable error message.")
        type: str = Field(None, description="A machine-readable error type identifier.")

    class ErrorResponseSchema(BaseModel):
        code: str = Field(..., description="Unique application-specific error code (e.g., 404_001).")
        detail: str = Field(..., description="A human-readable explanation of the error.")
        category: str = Field(..., description="Category of the error (e.g., ClientError, ServerError, Validation).")
        status_code: int = Field(..., description="HTTP status code equivalent, for consistent API/UI behavior.")
        metadata: Optional[Dict[str, Any]] = Field(None, description="Additional context or debugging information (e.e.g., project_id, invalid_value).")
        errors: Optional[List[ErrorDetail]] = Field(None, description="Detailed list of specific validation issues, if applicable.")

        class Config:
            json_schema_extra = {
                "example": {
                    "code": "404_001",
                    "detail": "Project with ID 'XYZ-123' not found.",
                    "category": "ClientError",
                    "status_code": 404,
                    "metadata": {"project_id": "XYZ-123", "requested_by": "<EMAIL>"}
                }
            }
    ```

*   **Benefits:**
    *   **Predictable API:** The UI always knows what structure to expect for an error.
    *   **Automated Parsing:** Pydantic allows for easy parsing and display of error messages.
    *   **Self-Documenting:** The schemas inherently document your error response formats.

---

### Exception Handling at Different Layers

This section details how each layer interacts with the defined error handling framework.

*   **Presentation Layer (UI/API Endpoints):**
    *   **Catch:** Catches `BaseApplicationException` or more general `Exception` types from service calls or API handlers.
    *   **Action:** Uses the `ErrorResponseSchema` to extract human-readable `detail` messages, `code`, and `metadata`.
        *   For UI: Displays these in pop-up dialogs, status bars, or inline form errors. For critical errors, provides a way to report a bug or save logs.
        *   For API: Global exception handlers (e.g., FastAPI's `@app.exception_handler`) transform `BaseApplicationException` instances into HTTP responses using `ErrorResponseSchema`, mapping `status_code` correctly.
*   **Service Layer ([`src/core/services/`](../../services/services-architecture.md)):**
    *   **Catch:** Catches more granular exceptions from the [Repository Layer](../../repositories/repositories-architecture.md) (e.g., SQLAlchemy `IntegrityError`, `NoResultFound`), [Calculations Layer](../../calculations/calculations-architecture.md) (`CalculationError`), [Standards Layer](../../standards/standards-architecture.md) (`StandardComplianceError`), etc.
    *   **Action:**
        *   **Translate:** Translates these lower-level exceptions into your custom `BaseApplicationException` hierarchy (e.g., `NoResultFound` becomes `NotFoundError`, `IntegrityError` becomes `DatabaseError` or a more specific `DuplicateEntryError`). This is where `ErrorFactory.create_exception()` is primarily used.
        *   **Enrich:** Adds relevant context to the exception's `metadata` (e.g., `project_id`, `component_name`, `input_values`) to aid debugging and future analysis.
        *   **Raise:** Re-raises your custom `BaseApplicationException` for the Presentation Layer/Middleware to handle.
*   **Repository Layer ([`src/core/repositories/`](../../repositories/repositories-architecture.md)):**
    *   **Catch:** Catches `sqlalchemy.exc` exceptions (e.g., `IntegrityError` for unique constraint violations, `DataError` for invalid data types, `NoResultFound` for specific queries).
    *   **Action:**
        *   **Translate:** Translates these database-specific exceptions into your custom `DatabaseError`, `NotFoundError`, or `DuplicateEntryError` using the `ErrorFactory`.
        *   **Re-raise:** Re-raises the custom exception. **Avoid exposing raw SQLAlchemy exceptions outside this layer.**
*   **Calculations Layer ([`src/core/calculations/`](../../calculations/calculations-architecture.md)), Standards Layer ([`src/core/standards/`](../../standards/standards-architecture.md)), Report Generation ([`src/core/reports/`](../../reports/reports-architecture.md)), Data Import ([`src/core/data_import/`](../../data_import/data_import-architecture.md)):**
    *   **Catch (Internal):** These layers will perform their internal validation and algorithmic checks.
    *   **Action:** When an error condition is met (e.g., invalid input for a calculation, standard violation, template rendering failure, parsing error), they will **raise specific custom exceptions from `src/core/errors/exceptions.py`** (e.g., `CalculationError`, `StandardComplianceError`, `ReportGenerationError`, `ImportError`). These exceptions should be constructed with detailed `metadata` providing context.
*   **Pydantic Validation ([Schema Layer](../../schemas/schemas-architecture.md)):**
    *   **Catch:** Pydantic's `ValidationError` occurs when input data doesn't conform to a defined schema (often at the [API Layer](../../../api/api-architecture.md)'s request parsing).
    *   **Action:** Catch `ValidationError` and convert it into your custom `DataValidationError` exception, populating its `metadata` (and potentially the `errors` list) with the detailed validation errors from Pydantic's `exc.errors()`.

This structured error handling approach, integrated into your proposed layered architecture, creates a robust, debuggable, and user-friendly application.
