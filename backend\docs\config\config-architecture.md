### Configuration Layer Architecture Specification

**1. Purpose & Role**
The Configuration Layer is responsible for centralizing, loading, validating, and providing application-wide settings and environment variables. Its primary purpose is to:
* **Decouple Configuration from Code:** Allow application behavior to be adjusted without modifying code, facilitating deployment across different environments.
* **Type Safety & Validation:** Ensure that configuration values are of the correct type and format, preventing runtime errors related to misconfigured settings.
* **Security:** Provide a secure mechanism for handling sensitive credentials and secrets.
* **Consistency:** Offer a single, consistent interface for all parts of the application to access settings.

**2. Location (`src/config/settings.py`)**
All core application settings are defined within a single, dedicated module: `src/config/settings.py`. This serves as the single source of truth for configuration.

**3. Framework/Library Choice (Pydantic `BaseSettings`)**
* **FastAPI Integration:** Leveraging Pydantic's `BaseSettings` (or `pydantic-settings` for Pydantic v2+) is the recommended approach.
* **Justification:**
    * **Automatic Loading:** `BaseSettings` automatically loads values from environment variables.
    * **`.env` File Support:** Supports loading settings from `.env` files (e.g., `DATABASE_URL="..."`) for local development convenience.
    * **Type Validation:** Provides automatic type validation and conversion for all defined settings (e.g., ensuring a port is an integer, a debug flag is a boolean).
    * **Default Values:** Allows defining sensible default values, which can then be overridden by environment variables or `.env` files.

**4. Core Responsibilities & Functionality**

* **Hierarchical Loading:** Settings are loaded in a defined order of precedence:
    1.  Default values (defined in `settings.py`).
    2.  Values from `.env` files (for local development).
    3.  Environment variables (highest precedence, ideal for deployment).
* **Schema Definition:** Defines a clear schema for all expected application settings using Pydantic fields and type hints.
* **Type Validation:** Performs runtime validation of loaded configuration values against the defined Pydantic schema, raising errors early if settings are malformed.
* **Sensitive Data Handling:** Encourages handling sensitive data (e.g., database passwords, JWT secrets) via environment variables, preventing them from being hardcoded or committed to version control.
* **Instance Provisioning:** Provides a single, globally accessible instance of the `Settings` object (`from src.config.settings import settings`) that all parts of the application can import and use.

**5. Key Application Settings Examples**

* **Application Metadata:**
    * `APP_NAME`: Name of the application (e.g., "Heat Tracing Design App").
    * `APP_VERSION`: Current version of the application (e.g., "1.0.0").
    * `APP_DESCRIPTION`: Brief description.
    * `ENVIRONMENT`: E.g., "development", "testing", "production".
    * `DEBUG`: Boolean flag for enabling debug features (e.g., debug endpoints, detailed error messages).
* **Database Configuration:**
    * `DATABASE_URL`: Connection string for the primary SQL Server database.
    * `SQLITE_DATABASE_PATH`: Path for the local SQLite database file (used in fallback).
    * `DB_ECHO`: Boolean to enable SQLAlchemy query logging.
* **Caching/Rate Limiting (Redis):**
    * `REDIS_ENABLED`: Boolean to enable/disable Redis integration.
    * `REDIS_URL`: Connection string for Redis.
    * `RATE_LIMIT_ENABLED`: Boolean to enable/disable rate limiting.
    * `RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE`: Default rate limit value.
* **Security:**
    * `SECRET_KEY`: Strong, randomly generated key for cryptographic operations (e.g., JWT signing).
    * `JWT_ALGORITHM`: Algorithm used for JWTs (e.g., "HS256").
    * `JWT_ACCESS_TOKEN_EXPIRE_MINUTES`: Expiration time for access tokens.
* **Logging:**
    * `LOG_LEVEL`: Minimum logging level (e.g., "INFO", "DEBUG", "WARNING").

**6. Interaction with Other Layers**

* **Application Root (`src/app.py`):** Uses settings for application initialization (title, version, database connection, middleware registration, lifespan events).
* **Middleware Layer (`src/middleware/`):** Utilizes settings to control behavior (e.g., `settings.DEBUG` for debug endpoints, `settings.RATE_LIMIT_ENABLED` for rate limiting, `settings.SECRET_KEY` for security middleware).
* **Core Services & Repositories (`src/core/`):** May access specific settings (e.g., `DB_ECHO` for SQLAlchemy, or feature flags for business logic variations).
* **Tests:** Use specific test configurations to ensure isolation and consistent test environments.

**7. Key Principles**

* **Environment-Driven Configuration:** Adheres to the 12-Factor App principle of storing configuration in the environment, which is paramount for deployment and security.
* **Centralized Management:** All application settings are managed from a single, well-defined location.
* **Type-Safe Access:** Provides strong typing for configuration values, reducing runtime errors.
* **Immutability:** Once loaded, the settings object is generally treated as immutable during runtime.
* **Security:** Promotes the use of environment variables for sensitive data, keeping secrets out of code repositories.