### Lifespan Events Architecture Specification

**1. Purpose & Role**
Lifespan events define the asynchronous startup and shutdown logic for the FastAPI application. Their primary purpose is to:
* **Resource Initialization:** Set up essential external resources (like database connections, caching clients) before the application starts accepting requests.
* **Graceful Shutdown:** Ensure all open connections and resources are properly closed and cleaned up when the application terminates, preventing data corruption or resource leaks.
* **Application Readiness:** Signal when the application is fully ready to handle incoming requests or gracefully shut down.

**2. FastAPI Mechanism (`asynccontextmanager`)**
FastAPI leverages Python's `asynccontextmanager` to define lifespan events. The code before the `yield` statement executes during application startup, and the code after the `yield` statement executes during application shutdown.

    **Example (Lifespan Context Manager):**

    ```python
    # Example in src/app.py
    from contextlib import asynccontextmanager
    from fastapi import FastAPI
    import logging
    # Assuming your logging config and db initialization functions exist
    from src.config.logging_config import setup_logging
    # from src.core.database.session import init_db, close_db_connections
    # from src.core.redis.client import connect_redis, close_redis_connection # Assuming Redis client
    # from src.config.settings import settings # Assuming settings for conditional initialization

    logger = logging.getLogger(__name__)

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        # Startup events (before yield)
        setup_logging()
        logger.info("Application startup initiated...")

        # Initialize database
        # await init_db(app) # Example database initialization function
        logger.info("Database initialized.")

        # Initialize other resources (e.g., Redis, external clients)
        # if settings.REDIS_ENABLED:
        #     await connect_redis(app)
        #     logger.info("Redis connected.")

        logger.info("Application startup complete.")
        yield
        # Shutdown events (after yield)
        logger.info("Application shutdown initiated...")

        # Close database connections
        # await close_db_connections(app)
        logger.info("Database connections closed.")

        # Close other resources (e.g., Redis)
        # if settings.REDIS_ENABLED:
        #     await close_redis_connection(app)
        #     logger.info("Redis connection closed.")

        logger.info("Application shutdown complete.")

    # In your FastAPI app instance:
    # app = FastAPI(lifespan=lifespan)
    ```

**3. Startup Events (`@asynccontextmanager` - before `yield`)**

*   **Logging System Initialization:**
    *   **Action:** Configure the centralized [logging system](how-to/how-to_logging.md) (`src/core/logging_config.py`) as the very first step.
    *   **Importance:** Ensures all subsequent startup and shutdown logs are captured correctly.

    See the [How-To: Configure Logging](how-to/how-to_logging.md) for more details.

*   **Database Initialization & Fallback Mechanism:**
    *   **Action:** Attempt to initialize the database connection.
    * **Primary Attempt (SQL Server):** The application will first attempt to connect to SQL Server using the `DATABASE_URL` provided in `src/config/settings.py`.
    * **Fallback to SQLite:** If the SQL Server connection fails (e.g., connection refused, invalid credentials) or if SQL Server is not configured, the application will automatically attempt to connect to an SQLite database (e.g., `sqlite:///./data.db`).
        * **Justification:** This fallback mechanism is crucial for ensuring the "fully offline" capability and robustness, allowing the application to function locally even if a preferred SQL Server connection is unavailable.
*   [SQLAlchemy Setup](../core/database/architecture.md): Initialize the SQLAlchemy Engine and Session factory ([`src/core/models/__init__.py`](../core/models/models-architecture.md)).
*   [Schema Creation](../core/database/migrations-architecture.md) (V1.0): For the initial version, `Base.metadata.create_all(engine)` will be called to create all necessary database tables if they do not already exist. For future production deployments, a more robust migration tool like [Alembic](../core/database/migrations-architecture.md) will be used, but this is sufficient for V1.0 desktop app.
*   Application State: The initialized SQLAlchemy Engine and Session factory will be stored in `app.state` for later retrieval via [dependency injection](../../dependency-management-architecture.md) (`get_db`).

    See the [Database Layer Architecture](../core/database/architecture.md) and [Model Layer Architecture](../core/models/models-architecture.md) for more details.
    * **Error Handling:** Critical errors during database initialization (both SQL Server and SQLite fallback) will cause the application to fail fast, preventing it from starting in an unrecoverable state.
*   Optional Redis Initialization:
    *   **Action:** If [`settings.REDIS_ENABLED`](../config/config-architecture.md) is `True` and [`settings.REDIS_URL`](../config/config-architecture.md) is configured, attempt to connect to Redis.
    *   **Purpose:** Primarily to support [rate limiting middleware](../../middleware/middleware-architecture.md).

    See the [Configuration Layer Architecture](../config/config-architecture.md) and [Middleware Layer Architecture](../../middleware/middleware-architecture.md) for more details.
    * **Error Handling:** Connection failures to Redis will be logged as warnings, but will *not* prevent the application from starting if Redis is optional. If Redis is considered critical, this behavior can be changed.
    * **Application State:** The initialized Redis client/connection pool will be stored in `app.state`.
* **Other Service Initialization (If Needed):**
    * Any other services that require one-time initialization or resource loading (e.g., loading large lookup tables into memory) would be performed here.

**4. Shutdown Events (`@asynccontextmanager` - after `yield`)**

* **Database Connection Closure:**
    * **Action:** Gracefully close all active database connections managed by the SQLAlchemy engine.
    * **Importance:** Prevents resource leaks and ensures any pending transactions are committed or rolled back.
* **Redis Client Closure:**
    * **Action:** If Redis was initialized, close its client or connection pool.
    * **Importance:** Releases network resources.
* **Other Resource Cleanup:** Any other resources initialized during startup (e.g., file handles, custom thread pools) will be properly shut down or released.
* **Logging System Shutdown:** Ensures all buffered logs are flushed.

**5. Error Handling During Lifespan**

* **Critical Failures:** If a core dependency (like the database, after both SQL Server and SQLite attempts) fails to initialize during startup, the application will log a critical error and **not proceed to start**. This ensures the application never enters an unrecoverable state.
* **Non-Critical Failures:** Failures in optional components (like Redis, if not critical) will be logged as warnings, allowing the application to proceed.

**6. Location**
The `lifespan` context manager function will be defined and associated with the FastAPI application instance primarily within `src/app.py`.

---
