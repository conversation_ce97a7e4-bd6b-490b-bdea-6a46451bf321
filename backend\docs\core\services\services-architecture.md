### Service Layer Architecture Specification

**1. Purpose & Role**
The Service Layer is the **heart of the application's business logic**. It encapsulates the application's core use cases, performs complex domain operations, applies business rules and validations, and orchestrates interactions between the various data access components ([Repositories](../repositories/repositories-architecture.md)) and other services. It effectively translates requests from the [API Layer](../../../api/api-architecture.md) into meaningful application actions.

**2. Structure (`src/core/services/`)**
Services are organized by their primary domain or functional area, promoting a clear separation of concerns and maintainability.

```
src/core/services/
├── __init__.py                 # Initializes the services package
├── project_service.py          # Business logic for Project management
├── component_service.py        # Business logic for Component catalog operations
├── heat_tracing_service.py     # Business logic for Pipe, Vessel, HTCircuit, ControlCircuit operations
├── electrical_service.py       # Business logic for ElectricalNode, CableRoute operations
├── switchboard_service.py      # Business logic for Switchboard management
├── feeder_service.py           # Business logic for Feeder management
├── calculation_service.py      # Handles complex calculations (heat loss, cable sizing, voltage drop)
├── report_service.py           # Orchestrates report generation (BOMs, schedules)
├── import_export_service.py    # Manages data import (parsing, validation, saving) and export
├── user_service.py             # Business logic for User management and authentication
└── audit_service.py            # (Optional) Service for retrieving/filtering audit logs (for admin UI)
```

**3. Core Responsibilities & Functionality**

* **Business Logic Implementation:** This is where the core algorithms, complex conditional logic, and domain-specific rules reside. Examples include:
    * Calculating heat loss for pipes/vessels.
    * Determining cable sizing based on current, length, and voltage drop limits.
    * Validating circuit design against engineering standards or project-specific rules.
    * Managing component lifecycle, including uniqueness checks and compatibility.
*   **Transaction Management:** Manages database transactions for operations that span multiple data changes or [repository calls](../repositories/repositories-architecture.md), ensuring atomicity (all or nothing) of business operations.

    See the [Transaction Management Architecture](../database/transaction-architecture.md) for more details.

*   **Orchestration:** Coordinates calls to multiple repositories or other services to fulfill a complex business request (e.g., "create project" might involve creating the `Project` record, default `Switchboards`, and `Feeders`).

    **Example (Orchestration in a Service Method):**

    ```python
    # Example in src/core/services/project_service.py
    from src.core.repositories.project_repository import ProjectRepository
    from src.core.repositories.switchboard_repository import SwitchboardRepository # Assuming this exists
    from src.core.schemas.project_schemas import ProjectCreate # Assuming this schema exists
    from src.core.models.project import Project # Assuming this model exists
    from src.core.database.transaction_manager import transactional # Assuming transactional decorator exists

    class ProjectService:
        def __init__(self, project_repository: ProjectRepository, switchboard_repository: SwitchboardRepository):
            self.project_repository = project_repository
            self.switchboard_repository = switchboard_repository

        @transactional # Ensures atomicity of the operation
        def create_project_with_defaults(self, project_data: ProjectCreate) -> Project:
            # 1. Create the main project record
            project = self.project_repository.create(project_data.model_dump())

            # 2. Create default associated entities (orchestration)
            # Create a default switchboard for the new project
            default_switchboard_data = {'name': 'Default Switchboard', 'project_id': project.id}
            self.switchboard_repository.create(default_switchboard_data)

            # You might call other services or repositories here to set up other defaults
            # e.g., self.report_service.setup_default_reports(project.id)

            return project # The @transactional decorator handles commit/rollback
    ```

*   **Business Validation:** Performs validation rules that go beyond basic data type checks (handled by Pydantic schemas), such as:
    * Uniqueness checks for business keys (e.g., unique project number).
    * Referential integrity at the business level (e.g., ensuring a pipe exists before assigning an HTCircuit to it).
    * Consistency checks across related data.
*   **Error Translation & Raising:** Catches lower-level exceptions (e.g., `sqlalchemy.exc.IntegrityError` from the [Repository Layer](../repositories/repositories-architecture.md), or `ValidationError` from [Pydantic](../../schemas/schemas-architecture.md) during internal schema conversions) and translates them into meaningful, high-level **custom application exceptions** (from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy)). These custom exceptions are then raised for the [API layer](../../../api/api-architecture.md)/global [error handler](../../errors/errors-architecture.md#3-error-handling-middleware-srccoreerrorserror_handlingpy) to catch and format.

    For a detailed explanation of error handling, see the [Error and Exception Handling Architecture](../../errors/errors-architecture.md).

*   **Data Transformation (Internal):** May perform necessary transformations between input/output [schemas (Pydantic)](../../schemas/schemas-architecture.md) and [ORM models](../models/models-architecture.md), or between different [ORM models](../models/models-architecture.md), as required by business logic.
* **Active Logging:** Logs significant business events and potential issues (e.g., "Project created," "Calculation initiated," "Warning: Insulation thickness out of recommended range").

**4. Interaction with Other Layers**

*   **API Layer ([`src/api/`](../../../api/api-architecture.md)):** The [API Layer](../../../api/api-architecture.md) consumes methods from the Service Layer to fulfill client requests. Services receive validated [Pydantic schemas](../../schemas/schemas-architecture.md) as input and typically return [ORM models](../models/models-architecture.md) or [Pydantic schemas](../../schemas/schemas-architecture.md) as output.
*   **Repository Layer ([`src/core/repositories/`](../repositories/repositories-architecture.md)):** Services depend heavily on the [Repository Layer](../repositories/repositories-architecture.md) for all data persistence operations (CRUD). [Services](services-architecture.md) *do not* directly interact with SQLAlchemy sessions or raw database queries.
*   **Schema Layer ([`src/core/schemas/`](../schemas/schemas-architecture.md)):** [Services](services-architecture.md) receive validated [Pydantic schemas](../../schemas/schemas-architecture.md) as input from the [API layer](../../../api/api-architecture.md) and may return [ORM models](../models/models-architecture.md) that the [API layer](../../../api/api-architecture.md) then serializes using [Pydantic schemas](../../schemas/schemas-architecture.md). [Services](services-architecture.md) might also use Pydantic for internal validation of intermediate data structures.
*   **Error Handling ([`src/core/errors/`](../../errors/errors-architecture.md)):** [Services](services-architecture.md) are responsible for raising [custom exceptions](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy) when business rules are violated or expected errors occur, allowing the global [error handling middleware](../../errors/errors-architecture.md#3-error-handling-middleware-srccoreerrorserror_handlingpy) to process them.
*   **Logging (`src/core/logging_config.py`):** [Services](services-architecture.md) leverage the centralized logging utility to record business-level events and debug information.

    See the [How-To: Configure Logging](../../../how-to/how-to_logging.md) for more details.

**5. Key Principles**

* **Single Responsibility:** Each service class focuses on a cohesive set of related business operations (e.g., `ProjectService` manages all project-related logic).
* **Dependency Injection:** Services receive their dependencies (e.g., instances of `ProjectRepository`, `CalculationService`) via their constructors, promoting testability and modularity.
* **No Direct DB Access:** Services are strictly decoupled from database implementation details. They rely entirely on the Repository Layer for data access.
* **Domain-Driven:** Services directly implement and enforce the rules and processes of the heat tracing design domain.
* **Testability:** Services are designed to be easily unit-tested by mocking their repository dependencies.

---