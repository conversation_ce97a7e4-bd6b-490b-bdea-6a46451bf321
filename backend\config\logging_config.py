# src/core/logging_config.py
import logging
import os

def setup_logging(log_file="app.log", level=logging.INFO):
    """
    Configures the application-wide logger.
    """
    logger = logging.getLogger('heat_tracing_app')
    logger.setLevel(level)

    # Create handlers
    console_handler = logging.StreamHandler()
    file_handler = logging.FileHandler(log_file)

    # Create formatters and add to handlers
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)')
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # Add handlers to the logger
    if not logger.handlers: # Avoid adding handlers multiple times
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)

    return logger

# Initialize the logger for the application
app_logger = setup_logging(log_file=os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../app.log'))